{"expo": {"name": "Habit Royale", "slug": "habit-royale", "version": "1.0.0", "orientation": "portrait", "icon": "assets/images/logoBig.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"softwareKeyboardLayoutMode": "pan", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#000000"}, "package": "com.varunveeraa.habitroyale"}, "web": {"bundler": "metro", "output": "server", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "assets/images/logoBig.png", "resizeMode": "contain", "backgroundColor": "#000000"}], "expo-font", "expo-maps"], "splash": {"image": "assets/images/logoBig.png", "resizeMode": "contain", "backgroundColor": "#000000"}, "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "b22ed3c5-6b08-43ea-8c7f-799a4202d79d"}}}}