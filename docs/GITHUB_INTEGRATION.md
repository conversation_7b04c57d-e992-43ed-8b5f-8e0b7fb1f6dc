# GitHub Integration Setup

This document explains how to set up GitHub OAuth integration for the Habit Royale app to enable commit verification for coding programs.

## Overview

The GitHub integration allows users to:
- Connect their GitHub account via OAuth
- Select repositories for commit verification
- Automatically verify daily coding commits
- Track coding habit progress

## Setup Instructions

### 1. Create GitHub OAuth App

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click "New OAuth App"
3. Fill in the application details:
   - **Application name**: `Habit Royale`
   - **Homepage URL**: Your app's homepage URL
   - **Application description**: `Habit tracking app with GitHub commit verification`
   - **Authorization callback URL**: 
     - For mobile: `habitroyale://github-auth`
     - For web: `https://yourdomain.com/auth/github/callback`

4. Click "Register application"
5. Copy the **Client ID** and generate a **Client Secret**

### 2. Configure Environment Variables

Add the following environment variables to your `.env` file:

```bash
EXPO_PUBLIC_GITHUB_CLIENT_ID=your_github_client_id_here
EXPO_PUBLIC_GITHUB_CLIENT_SECRET=your_github_client_secret_here
```

**Important**: Never commit your Client Secret to version control. Use environment variables or a secure configuration service.

### 3. Update App Configuration

The GitHub configuration is located in `config/github.ts`. The default configuration includes:

- **Scopes**: `['repo', 'user:email']` - Required to read repositories and user information
- **Min Commits**: `1` - Minimum commits required for verification
- **Verification Window**: `24` hours - How far back to check for commits

## How It Works

### User Flow

1. **Setup Phase** (CategorySetup):
   - User selects "coding" category for their program
   - App prompts for GitHub OAuth authentication
   - User authorizes the app and grants repository access
   - Connection status is saved to user's program setup

2. **Daily Submission** (CategoryInput/CommitInput):
   - User opens daily submission interface
   - App displays list of user's GitHub repositories
   - User selects the repository they've been working on
   - App verifies commits in the last 24 hours
   - Submission is marked as successful if commits are found

### Technical Implementation

#### Components

- **GitHubService** (`services/githubService.ts`): Handles OAuth flow and API calls
- **GitHubVerificationInput** (`components/verification/GitHubVerificationInput.tsx`): UI for repository selection
- **CategorySetup**: Updated to handle GitHub OAuth setup
- **CategoryInput/CommitInput**: Updated to display GitHub repositories

#### Data Flow

1. OAuth authentication stores access token in AsyncStorage
2. Repository list is fetched and cached
3. Commit verification checks the selected repository for recent commits
4. Verification result is stored with the submission

## Security Considerations

- Access tokens are stored locally using AsyncStorage
- Tokens are only used for read-only operations (repo and user:email scopes)
- Users can revoke access at any time through their GitHub settings
- Client Secret should be kept secure and not exposed in client code

## Troubleshooting

### Common Issues

1. **Authentication fails**: Check that Client ID and redirect URI match your OAuth app settings
2. **No repositories shown**: Ensure the user has granted the 'repo' scope
3. **Commit verification fails**: Check that the user has made commits in the selected repository within the verification window

### Testing

For testing purposes, you can:
1. Create a test repository
2. Make test commits
3. Verify the integration detects the commits correctly

## API Limits

GitHub API has rate limits:
- **Authenticated requests**: 5,000 per hour
- **Unauthenticated requests**: 60 per hour

The app uses authenticated requests, so the limit should be sufficient for normal usage.

## Future Enhancements

Potential improvements:
- Support for private repositories
- Commit quality verification (e.g., minimum lines changed)
- Integration with specific branches
- Support for GitHub organizations
- Webhook integration for real-time verification
