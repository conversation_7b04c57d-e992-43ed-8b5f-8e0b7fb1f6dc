import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  StyleSheet,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { githubService, GitHubRepository, GitHubUser } from '@/services/githubService';

interface GitHubVerificationInputProps {
  onSubmit: (data: { repository: string; commits: any[] }) => void;
  onValidationChange: (isValid: boolean) => void;
  disabled?: boolean;
}

export const GitHubVerificationInput: React.FC<GitHubVerificationInputProps> = ({
  onSubmit,
  onValidationChange,
  disabled = false,
}) => {
  const { colors } = useTheme();
  const [repositories, setRepositories] = useState<GitHubRepository[]>([]);
  const [selectedRepo, setSelectedRepo] = useState<GitHubRepository | null>(null);
  const [loading, setLoading] = useState(true);
  const [verifying, setVerifying] = useState(false);
  const [userData, setUserData] = useState<GitHubUser | null>(null);

  useEffect(() => {
    loadGitHubData();
  }, []);

  useEffect(() => {
    onValidationChange(!!selectedRepo);
  }, [selectedRepo, onValidationChange]);

  const loadGitHubData = async () => {
    try {
      setLoading(true);
      await githubService.initialize();

      if (!githubService.isAuthenticated()) {
        Alert.alert(
          'GitHub Not Connected',
          'Please connect your GitHub account first in the setup step.'
        );
        return;
      }

      const [user, repos] = await Promise.all([
        githubService.getUserData(),
        githubService.getRepositories(),
      ]);

      setUserData(user);
      setRepositories(repos);
    } catch (error) {
      console.error('Failed to load GitHub data:', error);
      Alert.alert(
        'GitHub Error',
        'Failed to load your GitHub repositories. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleRepositorySelect = (repo: GitHubRepository) => {
    setSelectedRepo(repo);
  };

  const handleSubmit = async () => {
    if (!selectedRepo) {
      Alert.alert('Selection Required', 'Please select a repository first.');
      return;
    }

    try {
      setVerifying(true);
      
      // Verify commits in the last 24 hours
      const verification = await githubService.verifyCommits(selectedRepo.full_name, 1, 24);
      
      if (verification.verified) {
        onSubmit({
          repository: selectedRepo.full_name,
          commits: verification.commits,
        });
        
        Alert.alert(
          'Verification Successful',
          `Found ${verification.commitCount} commit(s) in the last 24 hours.`
        );
      } else {
        Alert.alert(
          'No Recent Commits',
          'No commits found in the selected repository within the last 24 hours. Please make sure you have committed code recently.'
        );
      }
    } catch (error) {
      console.error('GitHub verification error:', error);
      Alert.alert(
        'Verification Failed',
        'Failed to verify your commits. Please try again.'
      );
    } finally {
      setVerifying(false);
    }
  };

  const renderRepository = ({ item }: { item: GitHubRepository }) => (
    <TouchableOpacity
      style={[
        styles.repositoryItem,
        {
          backgroundColor: colors.surface,
          borderColor: selectedRepo?.id === item.id ? colors.primary : colors.border,
        },
        selectedRepo?.id === item.id && styles.selectedRepository,
      ]}
      onPress={() => handleRepositorySelect(item)}
      disabled={disabled}
    >
      <View style={styles.repositoryHeader}>
        <MaterialIcons
          name={item.private ? 'lock' : 'public'}
          size={16}
          color={item.private ? colors.warning : colors.success}
        />
        <Text style={[styles.repositoryName, { color: colors.text }]} numberOfLines={1}>
          {item.name}
        </Text>
        {selectedRepo?.id === item.id && (
          <MaterialIcons name="check-circle" size={20} color={colors.primary} />
        )}
      </View>

      {item.description && (
        <Text style={[styles.repositoryDescription, { color: colors.textSecondary }]} numberOfLines={2}>
          {item.description}
        </Text>
      )}

      <Text style={[styles.repositoryUpdated, { color: colors.textSecondary }]}>
        Updated: {new Date(item.updated_at).toLocaleDateString()}
      </Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading your GitHub repositories...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {userData && (
        <View style={styles.userInfo}>
          <MaterialIcons name="account-circle" size={24} color={colors.primary} />
          <Text style={[styles.userText, { color: colors.text }]}>
            Connected as: {userData.login}
          </Text>
        </View>
      )}

      <Text style={[styles.instructionText, { color: colors.textSecondary }]}>
        Select a repository to verify your recent commits:
      </Text>

      <FlatList
        data={repositories}
        renderItem={renderRepository}
        keyExtractor={(item) => item.id.toString()}
        style={styles.repositoryList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialIcons name="folder-open" size={48} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No repositories found
            </Text>
          </View>
        }
      />

      {selectedRepo && (
        <TouchableOpacity
          style={[
            styles.submitButton,
            { backgroundColor: colors.primary },
            (disabled || verifying) && styles.disabledButton,
          ]}
          onPress={handleSubmit}
          disabled={disabled || verifying}
        >
          {verifying ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <MaterialIcons name="verified" size={20} color="#fff" />
              <Text style={styles.submitButtonText}>Verify Commits</Text>
            </>
          )}
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  userText: {
    marginLeft: 12,
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
  },
  instructionText: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    marginBottom: 20,
    textAlign: 'center',
    lineHeight: 20,
  },
  repositoryList: {
    flex: 1,
    marginBottom: 16,
  },
  repositoryItem: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  selectedRepository: {
    borderWidth: 2,
    transform: [{ scale: 1.02 }],
  },
  repositoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  repositoryName: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    marginLeft: 12,
  },
  repositoryDescription: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
    marginTop: 6,
    marginLeft: 28,
    lineHeight: 18,
    opacity: 0.8,
  },
  repositoryUpdated: {
    fontSize: 11,
    fontFamily: 'MontserratRegular',
    marginTop: 8,
    marginLeft: 28,
    opacity: 0.6,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 48,
    flex: 1,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    opacity: 0.6,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  disabledButton: {
    opacity: 0.6,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'MontserratBold',
    marginLeft: 8,
  },
});
