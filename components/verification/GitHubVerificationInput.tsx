import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  StyleSheet,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { githubService, GitHubRepository, GitHubUser } from '@/services/githubService';

interface GitHubVerificationInputProps {
  onSubmit: (data: { repository: string; commits: any[] }) => void;
  onValidationChange: (isValid: boolean) => void;
  disabled?: boolean;
}

export const GitHubVerificationInput: React.FC<GitHubVerificationInputProps> = ({
  onSubmit,
  onValidationChange,
  disabled = false,
}) => {
  const { colors } = useTheme();
  const [repositories, setRepositories] = useState<GitHubRepository[]>([]);
  const [selectedRepo, setSelectedRepo] = useState<GitHubRepository | null>(null);
  const [loading, setLoading] = useState(true);
  const [verifying, setVerifying] = useState(false);
  const [userData, setUserData] = useState<GitHubUser | null>(null);

  useEffect(() => {
    loadGitHubData();
  }, []);

  useEffect(() => {
    onValidationChange(!!selectedRepo);
  }, [selectedRepo, onValidationChange]);

  const loadGitHubData = async () => {
    try {
      console.log('GitHubVerificationInput: Starting to load GitHub data');
      setLoading(true);
      await githubService.initialize();

      const isAuth = githubService.isAuthenticated();
      console.log('GitHubVerificationInput: Is authenticated?', isAuth);

      if (!isAuth) {
        console.log('GitHubVerificationInput: Not authenticated, showing alert');
        Alert.alert(
          'GitHub Not Connected',
          'Please connect your GitHub account first in the setup step.'
        );
        return;
      }

      console.log('GitHubVerificationInput: Fetching user data and repositories');
      const [user, repos] = await Promise.all([
        githubService.getUserData(),
        githubService.getRepositories(),
      ]);

      console.log('GitHubVerificationInput: User data:', user?.login);
      console.log('GitHubVerificationInput: Repositories count:', repos.length);
      console.log('GitHubVerificationInput: Repository names:', repos.map(r => r.name).slice(0, 5));

      setUserData(user);
      setRepositories(repos);
    } catch (error) {
      console.error('Failed to load GitHub data:', error);
      Alert.alert(
        'GitHub Error',
        'Failed to load your GitHub repositories. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleRepositorySelect = (repo: GitHubRepository) => {
    setSelectedRepo(repo);
  };

  const handleSubmit = async () => {
    if (!selectedRepo) {
      Alert.alert('Selection Required', 'Please select a repository first.');
      return;
    }

    try {
      setVerifying(true);
      
      // Verify commits in the last 24 hours
      const verification = await githubService.verifyCommits(selectedRepo.full_name, 1, 24);
      
      if (verification.verified) {
        onSubmit({
          repository: selectedRepo.full_name,
          commits: verification.commits,
        });
        
        Alert.alert(
          'Verification Successful',
          `Found ${verification.commitCount} commit(s) in the last 24 hours.`
        );
      } else {
        Alert.alert(
          'No Recent Commits',
          'No commits found in the selected repository within the last 24 hours. Please make sure you have committed code recently.'
        );
      }
    } catch (error) {
      console.error('GitHub verification error:', error);
      Alert.alert(
        'Verification Failed',
        'Failed to verify your commits. Please try again.'
      );
    } finally {
      setVerifying(false);
    }
  };

  const renderRepository = ({ item }: { item: GitHubRepository }) => {
    console.log('GitHubVerificationInput: Rendering repository:', item.name);
    return (
      <TouchableOpacity
        style={[
          styles.repositoryItem,
          {
            backgroundColor: colors.surface || '#f5f5f5', // Fallback background color
            borderColor: selectedRepo?.id === item.id ? colors.primary : (colors.border || '#ddd'),
          },
          selectedRepo?.id === item.id && styles.selectedRepository,
        ]}
        onPress={() => handleRepositorySelect(item)}
        disabled={disabled}
      >
        <View style={styles.repositoryHeader}>
          <MaterialIcons
            name={item.private ? 'lock' : 'public'}
            size={16}
            color={item.private ? colors.warning : colors.success}
          />
          <Text style={[styles.repositoryName, { color: colors.text || '#000' }]} numberOfLines={1}>
            {item.name}
          </Text>
          {selectedRepo?.id === item.id && (
            <MaterialIcons name="check-circle" size={20} color={colors.primary} />
          )}
        </View>

        {item.description && (
          <Text style={[styles.repositoryDescription, { color: colors.textSecondary || '#666' }]} numberOfLines={2}>
            {item.description}
          </Text>
        )}

        <Text style={[styles.repositoryUpdated, { color: colors.textSecondary || '#666' }]}>
          Updated: {new Date(item.updated_at).toLocaleDateString()}
        </Text>
      </TouchableOpacity>
    );
  };

  if (loading) {
    console.log('GitHubVerificationInput: Rendering loading state');
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading your GitHub repositories...
        </Text>
      </View>
    );
  }

  console.log('GitHubVerificationInput: Rendering main UI with', repositories.length, 'repositories');
  console.log('GitHubVerificationInput: User data exists?', !!userData);
  console.log('GitHubVerificationInput: Colors:', {
    surface: colors.surface,
    text: colors.text,
    textSecondary: colors.textSecondary,
    border: colors.border,
    primary: colors.primary
  });

  return (
    <View style={styles.container}>
      {userData && (
        <View style={styles.userInfo}>
          <MaterialIcons name="account-circle" size={24} color={colors.primary} />
          <Text style={[styles.userText, { color: colors.text }]}>
            Connected as: {userData.login}
          </Text>
        </View>
      )}

      <Text style={[styles.instructionText, { color: colors.textSecondary }]}>
        Select a repository to verify your recent commits:
      </Text>

      <FlatList
        data={repositories}
        renderItem={renderRepository}
        keyExtractor={(item) => item.id.toString()}
        style={styles.repositoryList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialIcons name="folder-open" size={48} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No repositories found
            </Text>
          </View>
        }
      />

      {selectedRepo && (
        <TouchableOpacity
          style={[
            styles.submitButton,
            { backgroundColor: colors.primary },
            (disabled || verifying) && styles.disabledButton,
          ]}
          onPress={handleSubmit}
          disabled={disabled || verifying}
        >
          {verifying ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <MaterialIcons name="verified" size={20} color="#fff" />
              <Text style={styles.submitButtonText}>Verify Commits</Text>
            </>
          )}
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    minHeight: 400, // Ensure minimum height
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    minHeight: 200,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  userText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
  },
  instructionText: {
    fontSize: 14,
    marginBottom: 16,
    textAlign: 'center',
  },
  repositoryList: {
    flex: 1,
    marginBottom: 16,
    minHeight: 200, // Ensure minimum height for the list
  },
  repositoryItem: {
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1,
    minHeight: 80, // Ensure each item has minimum height
  },
  selectedRepository: {
    borderWidth: 2,
  },
  repositoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  repositoryName: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  repositoryDescription: {
    fontSize: 14,
    marginTop: 4,
    lineHeight: 20,
  },
  repositoryUpdated: {
    fontSize: 12,
    marginTop: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  disabledButton: {
    opacity: 0.6,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});
