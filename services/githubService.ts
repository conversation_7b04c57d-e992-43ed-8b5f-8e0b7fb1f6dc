import AsyncStorage from '@react-native-async-storage/async-storage';
import * as WebBrowser from 'expo-web-browser';
import * as Linking from 'expo-linking';
import { Platform } from 'react-native';
import { GITHUB_CONFIG } from '@/config/github';
import { getFunctions, httpsCallable } from 'firebase/functions';

// GitHub OAuth configuration with redirect URI
const OAUTH_CONFIG = {
  ...GITHUB_CONFIG,
  REDIRECT_URI: Platform.OS === 'web'
    ? 'https://us-central1-betonself.cloudfunctions.net/githubOAuthCallback'
    : Linking.createURL('github-auth'),
};

// Storage keys
const STORAGE_KEYS = {
  ACCESS_TOKEN: 'github_access_token',
  USER_DATA: 'github_user_data',
  REPOSITORIES: 'github_repositories',
};

export interface GitHubUser {
  id: number;
  login: string;
  name: string;
  email: string;
  avatar_url: string;
}

export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string;
  private: boolean;
  html_url: string;
  updated_at: string;
}

export interface GitHubCommit {
  sha: string;
  commit: {
    author: {
      name: string;
      email: string;
      date: string;
    };
    message: string;
  };
  html_url: string;
}

export class GitHubService {
  private static instance: GitHubService;
  private accessToken: string | null = null;

  private constructor() {}

  public static getInstance(): GitHubService {
    if (!GitHubService.instance) {
      GitHubService.instance = new GitHubService();
    }
    return GitHubService.instance;
  }

  // Initialize the service and load stored token
  public async initialize(): Promise<void> {
    try {
      this.accessToken = await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    } catch (error) {
      console.error('Failed to initialize GitHub service:', error);
    }
  }

  // Check if user is authenticated
  public isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  // Start OAuth flow
  public async authenticate(): Promise<{ success: boolean; error?: string }> {
    try {
      const authUrl = this.buildAuthUrl();
      
      if (Platform.OS === 'web') {
        // For web, use popup window
        const result = await this.authenticateWeb(authUrl);
        return result;
      } else {
        // For mobile, use WebBrowser
        const result = await this.authenticateMobile(authUrl);
        return result;
      }
    } catch (error) {
      console.error('GitHub authentication error:', error);
      return { success: false, error: 'Authentication failed' };
    }
  }

  // Build GitHub OAuth URL
  private buildAuthUrl(state?: string): string {
    const authState = state || Math.random().toString(36).substring(7);
    const params = new URLSearchParams({
      client_id: OAUTH_CONFIG.CLIENT_ID,
      redirect_uri: OAUTH_CONFIG.REDIRECT_URI,
      scope: OAUTH_CONFIG.SCOPES.join(' '),
      state: authState,
    });

    // Debug logging
    console.log('GitHub OAuth Config:', {
      CLIENT_ID: OAUTH_CONFIG.CLIENT_ID,
      REDIRECT_URI: OAUTH_CONFIG.REDIRECT_URI,
      SCOPES: OAUTH_CONFIG.SCOPES,
      Platform: Platform.OS
    });

    return `https://github.com/login/oauth/authorize?${params.toString()}`;
  }

  // Web authentication using popup and Cloud Function
  private async authenticateWeb(authUrl: string): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve) => {
      const popup = window.open(authUrl, 'github-auth', 'width=600,height=700');

      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed);
          resolve({ success: false, error: 'Authentication cancelled' });
        }
      }, 1000);

      // Listen for the success message from the Cloud Function callback page
      const handleMessage = async (event: MessageEvent) => {
        // Accept messages from the Cloud Function domain
        if (!event.origin.includes('cloudfunctions.net') && event.origin !== window.location.origin) return;

        if (event.data.type === 'GITHUB_AUTH_SUCCESS') {
          clearInterval(checkClosed);
          popup?.close();
          window.removeEventListener('message', handleMessage);

          const { state, user } = event.data;

          // Retrieve the token from Cloud Function using the state
          const tokenResult = await this.retrieveTokenFromCloudFunction(state);
          if (tokenResult.success) {
            // Store user data
            await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user));
          }
          resolve(tokenResult);
        }
      };

      window.addEventListener('message', handleMessage);
    });
  }

  // Mobile authentication using WebBrowser
  private async authenticateMobile(authUrl: string): Promise<{ success: boolean; error?: string }> {
    const result = await WebBrowser.openAuthSessionAsync(
      authUrl,
      OAUTH_CONFIG.REDIRECT_URI,
      {
        showInRecents: true,
        createTask: false
      }
    );

    if (result.type === 'success' && result.url) {
      const url = new URL(result.url);
      const code = url.searchParams.get('code');

      if (code) {
        return await this.exchangeCodeForToken(code);
      }
    }

    return { success: false, error: 'Authentication cancelled or failed' };
  }

  // Retrieve token from Cloud Function using state parameter
  private async retrieveTokenFromCloudFunction(state: string): Promise<{ success: boolean; error?: string }> {
    try {
      const functions = getFunctions();
      const getGitHubToken = httpsCallable(functions, 'getGitHubToken');

      const result = await getGitHubToken({ state });
      const data = result.data as any;

      if (data.success && data.accessToken) {
        this.accessToken = data.accessToken;
        await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, this.accessToken!);

        // Store user data if provided
        if (data.user) {
          await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(data.user));
        }

        return { success: true };
      } else {
        return { success: false, error: 'Failed to retrieve access token from Cloud Function' };
      }
    } catch (error) {
      console.error('Cloud Function token retrieval error:', error);
      return { success: false, error: 'Failed to retrieve token from server' };
    }
  }

  // Exchange authorization code for access token (for mobile)
  private async exchangeCodeForToken(code: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch('https://github.com/login/oauth/access_token', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          client_id: OAUTH_CONFIG.CLIENT_ID,
          client_secret: OAUTH_CONFIG.CLIENT_SECRET,
          code,
        }),
      });

      const data = await response.json();
      
      if (data.access_token) {
        this.accessToken = data.access_token;
        await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, this.accessToken!);
        
        // Fetch and store user data
        await this.fetchAndStoreUserData();
        
        return { success: true };
      } else {
        return { success: false, error: data.error_description || 'Failed to get access token' };
      }
    } catch (error) {
      console.error('Token exchange error:', error);
      return { success: false, error: 'Failed to exchange code for token' };
    }
  }

  // Fetch user data from GitHub API
  private async fetchAndStoreUserData(): Promise<void> {
    if (!this.accessToken) return;

    try {
      const response = await fetch('https://api.github.com/user', {
        headers: {
          'Authorization': `token ${this.accessToken}`,
          'Accept': 'application/vnd.github.v3+json',
        },
      });

      if (response.ok) {
        const userData = await response.json();
        await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
      }
    } catch (error) {
      console.error('Failed to fetch user data:', error);
    }
  }

  // Get stored user data
  public async getUserData(): Promise<GitHubUser | null> {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Failed to get user data:', error);
      return null;
    }
  }

  // Fetch user repositories
  public async getRepositories(): Promise<GitHubRepository[]> {
    if (!this.accessToken) {
      console.error('GitHub getRepositories: No access token available');
      throw new Error('Not authenticated');
    }

    console.log('GitHub getRepositories: Fetching repositories with token:', this.accessToken?.substring(0, 10) + '...');

    try {
      const response = await fetch('https://api.github.com/user/repos?sort=updated&per_page=100', {
        headers: {
          'Authorization': `token ${this.accessToken}`,
          'Accept': 'application/vnd.github.v3+json',
        },
      });

      console.log('GitHub getRepositories: Response status:', response.status);

      if (response.ok) {
        const repositories = await response.json();
        console.log('GitHub getRepositories: Fetched', repositories.length, 'repositories');
        console.log('GitHub getRepositories: First few repos:', repositories.slice(0, 3).map((r: any) => r.name));

        await AsyncStorage.setItem(STORAGE_KEYS.REPOSITORIES, JSON.stringify(repositories));
        return repositories;
      } else {
        const errorText = await response.text();
        console.error('GitHub getRepositories: API error:', response.status, errorText);
        throw new Error(`Failed to fetch repositories: ${response.status} ${errorText}`);
      }
    } catch (error) {
      console.error('Failed to fetch repositories:', error);
      // Try to return cached repositories
      const cached = await AsyncStorage.getItem(STORAGE_KEYS.REPOSITORIES);
      const cachedRepos = cached ? JSON.parse(cached) : [];
      console.log('GitHub getRepositories: Returning cached repositories:', cachedRepos.length);
      return cachedRepos;
    }
  }

  // Get commits for a repository within a time window
  public async getRecentCommits(
    repoFullName: string, 
    since: Date, 
    author?: string
  ): Promise<GitHubCommit[]> {
    if (!this.accessToken) {
      throw new Error('Not authenticated');
    }

    try {
      const params = new URLSearchParams({
        since: since.toISOString(),
        per_page: '100',
      });

      if (author) {
        params.append('author', author);
      }

      const response = await fetch(
        `https://api.github.com/repos/${repoFullName}/commits?${params.toString()}`,
        {
          headers: {
            'Authorization': `token ${this.accessToken}`,
            'Accept': 'application/vnd.github.v3+json',
          },
        }
      );

      if (response.ok) {
        return await response.json();
      } else {
        throw new Error('Failed to fetch commits');
      }
    } catch (error) {
      console.error('Failed to fetch commits:', error);
      return [];
    }
  }

  // Verify if user has made minimum commits in the last 24 hours
  public async verifyCommits(
    repoFullName: string,
    minCommits: number = OAUTH_CONFIG.MIN_COMMITS,
    hoursBack: number = OAUTH_CONFIG.VERIFICATION_WINDOW_HOURS
  ): Promise<{ verified: boolean; commitCount: number; commits: GitHubCommit[] }> {
    try {
      const since = new Date(Date.now() - hoursBack * 60 * 60 * 1000);
      const userData = await this.getUserData();
      const commits = await this.getRecentCommits(repoFullName, since, userData?.login);
      
      return {
        verified: commits.length >= minCommits,
        commitCount: commits.length,
        commits,
      };
    } catch (error) {
      console.error('Failed to verify commits:', error);
      return { verified: false, commitCount: 0, commits: [] };
    }
  }

  // Sign out and clear stored data
  public async signOut(): Promise<void> {
    try {
      this.accessToken = null;
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.ACCESS_TOKEN,
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.REPOSITORIES,
      ]);
    } catch (error) {
      console.error('Failed to sign out:', error);
    }
  }
}

// Export singleton instance
export const githubService = GitHubService.getInstance();
